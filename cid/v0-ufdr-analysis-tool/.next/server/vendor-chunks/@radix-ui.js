"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui";
exports.ids = ["vendor-chunks/@radix-ui"];
exports.modules = {

/***/ "(ssr)/./node_modules/@radix-ui/number/dist/index.mjs":
/*!******************************************************!*\
  !*** ./node_modules/@radix-ui/number/dist/index.mjs ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clamp: () => (/* binding */ clamp)\n/* harmony export */ });\n// packages/core/number/src/number.ts\nfunction clamp(value, [min, max]) {\n  return Math.min(max, Math.max(min, value));\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL251bWJlci9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvbnVtYmVyL2Rpc3QvaW5kZXgubWpzPzNjODEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9udW1iZXIvc3JjL251bWJlci50c1xuZnVuY3Rpb24gY2xhbXAodmFsdWUsIFttaW4sIG1heF0pIHtcbiAgcmV0dXJuIE1hdGgubWluKG1heCwgTWF0aC5tYXgobWluLCB2YWx1ZSkpO1xufVxuZXhwb3J0IHtcbiAgY2xhbXBcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs":
/*!*********************************************************!*\
  !*** ./node_modules/@radix-ui/primitive/dist/index.mjs ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeEventHandlers: () => (/* binding */ composeEventHandlers)\n/* harmony export */ });\n// packages/core/primitive/src/primitive.tsx\nfunction composeEventHandlers(originalEventHandler, ourEventHandler, { checkForDefaultPrevented = true } = {}) {\n  return function handleEvent(event) {\n    originalEventHandler?.(event);\n    if (checkForDefaultPrevented === false || !event.defaultPrevented) {\n      return ourEventHandler?.(event);\n    }\n  };\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3ByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSx1RUFBdUUsa0NBQWtDLElBQUk7QUFDN0c7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcHJpbWl0aXZlL2Rpc3QvaW5kZXgubWpzP2ViMjEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvY29yZS9wcmltaXRpdmUvc3JjL3ByaW1pdGl2ZS50c3hcbmZ1bmN0aW9uIGNvbXBvc2VFdmVudEhhbmRsZXJzKG9yaWdpbmFsRXZlbnRIYW5kbGVyLCBvdXJFdmVudEhhbmRsZXIsIHsgY2hlY2tGb3JEZWZhdWx0UHJldmVudGVkID0gdHJ1ZSB9ID0ge30pIHtcbiAgcmV0dXJuIGZ1bmN0aW9uIGhhbmRsZUV2ZW50KGV2ZW50KSB7XG4gICAgb3JpZ2luYWxFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgaWYgKGNoZWNrRm9yRGVmYXVsdFByZXZlbnRlZCA9PT0gZmFsc2UgfHwgIWV2ZW50LmRlZmF1bHRQcmV2ZW50ZWQpIHtcbiAgICAgIHJldHVybiBvdXJFdmVudEhhbmRsZXI/LihldmVudCk7XG4gICAgfVxuICB9O1xufVxuZXhwb3J0IHtcbiAgY29tcG9zZUV2ZW50SGFuZGxlcnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@radix-ui/react-avatar/dist/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Avatar: () => (/* binding */ Avatar),\n/* harmony export */   AvatarFallback: () => (/* binding */ AvatarFallback),\n/* harmony export */   AvatarImage: () => (/* binding */ AvatarImage),\n/* harmony export */   Fallback: () => (/* binding */ Fallback),\n/* harmony export */   Image: () => (/* binding */ Image),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   createAvatarScope: () => (/* binding */ createAvatarScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Avatar,AvatarFallback,AvatarImage,Fallback,Image,Root,createAvatarScope auto */ // packages/react/avatar/src/Avatar.tsx\n\n\n\n\n\n\nvar AVATAR_NAME = \"Avatar\";\nvar [createAvatarContext, createAvatarScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(AVATAR_NAME);\nvar [AvatarProvider, useAvatarContext] = createAvatarContext(AVATAR_NAME);\nvar Avatar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, ...avatarProps } = props;\n    const [imageLoadingStatus, setImageLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(AvatarProvider, {\n        scope: __scopeAvatar,\n        imageLoadingStatus,\n        onImageLoadingStatusChange: setImageLoadingStatus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n            ...avatarProps,\n            ref: forwardedRef\n        })\n    });\n});\nAvatar.displayName = AVATAR_NAME;\nvar IMAGE_NAME = \"AvatarImage\";\nvar AvatarImage = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, src, onLoadingStatusChange = ()=>{}, ...imageProps } = props;\n    const context = useAvatarContext(IMAGE_NAME, __scopeAvatar);\n    const imageLoadingStatus = useImageLoadingStatus(src, imageProps.referrerPolicy);\n    const handleLoadingStatusChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_4__.useCallbackRef)((status)=>{\n        onLoadingStatusChange(status);\n        context.onImageLoadingStatusChange(status);\n    });\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        if (imageLoadingStatus !== \"idle\") {\n            handleLoadingStatusChange(imageLoadingStatus);\n        }\n    }, [\n        imageLoadingStatus,\n        handleLoadingStatusChange\n    ]);\n    return imageLoadingStatus === \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.img, {\n        ...imageProps,\n        ref: forwardedRef,\n        src\n    }) : null;\n});\nAvatarImage.displayName = IMAGE_NAME;\nvar FALLBACK_NAME = \"AvatarFallback\";\nvar AvatarFallback = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeAvatar, delayMs, ...fallbackProps } = props;\n    const context = useAvatarContext(FALLBACK_NAME, __scopeAvatar);\n    const [canRender, setCanRender] = react__WEBPACK_IMPORTED_MODULE_0__.useState(delayMs === void 0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (delayMs !== void 0) {\n            const timerId = window.setTimeout(()=>setCanRender(true), delayMs);\n            return ()=>window.clearTimeout(timerId);\n        }\n    }, [\n        delayMs\n    ]);\n    return canRender && context.imageLoadingStatus !== \"loaded\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_3__.Primitive.span, {\n        ...fallbackProps,\n        ref: forwardedRef\n    }) : null;\n});\nAvatarFallback.displayName = FALLBACK_NAME;\nfunction useImageLoadingStatus(src, referrerPolicy) {\n    const [loadingStatus, setLoadingStatus] = react__WEBPACK_IMPORTED_MODULE_0__.useState(\"idle\");\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_5__.useLayoutEffect)(()=>{\n        if (!src) {\n            setLoadingStatus(\"error\");\n            return;\n        }\n        let isMounted = true;\n        const image = new window.Image();\n        const updateStatus = (status)=>()=>{\n                if (!isMounted) return;\n                setLoadingStatus(status);\n            };\n        setLoadingStatus(\"loading\");\n        image.onload = updateStatus(\"loaded\");\n        image.onerror = updateStatus(\"error\");\n        image.src = src;\n        if (referrerPolicy) {\n            image.referrerPolicy = referrerPolicy;\n        }\n        return ()=>{\n            isMounted = false;\n        };\n    }, [\n        src,\n        referrerPolicy\n    ]);\n    return loadingStatus;\n}\nvar Root = Avatar;\nvar Image = AvatarImage;\nvar Fallback = AvatarFallback;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-avatar/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWNvbXBvc2UtcmVmcy9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSx3QkFBd0IscUJBQXFCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLFlBQVk7QUFDWjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyw4Q0FBaUI7QUFDMUI7QUFJRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtY29tcG9zZS1yZWZzL2Rpc3QvaW5kZXgubWpzPzA5OGEiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFja2FnZXMvcmVhY3QvY29tcG9zZS1yZWZzL3NyYy9jb21wb3NlUmVmcy50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuZnVuY3Rpb24gc2V0UmVmKHJlZiwgdmFsdWUpIHtcbiAgaWYgKHR5cGVvZiByZWYgPT09IFwiZnVuY3Rpb25cIikge1xuICAgIHJldHVybiByZWYodmFsdWUpO1xuICB9IGVsc2UgaWYgKHJlZiAhPT0gbnVsbCAmJiByZWYgIT09IHZvaWQgMCkge1xuICAgIHJlZi5jdXJyZW50ID0gdmFsdWU7XG4gIH1cbn1cbmZ1bmN0aW9uIGNvbXBvc2VSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIChub2RlKSA9PiB7XG4gICAgbGV0IGhhc0NsZWFudXAgPSBmYWxzZTtcbiAgICBjb25zdCBjbGVhbnVwcyA9IHJlZnMubWFwKChyZWYpID0+IHtcbiAgICAgIGNvbnN0IGNsZWFudXAgPSBzZXRSZWYocmVmLCBub2RlKTtcbiAgICAgIGlmICghaGFzQ2xlYW51cCAmJiB0eXBlb2YgY2xlYW51cCA9PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgaGFzQ2xlYW51cCA9IHRydWU7XG4gICAgICB9XG4gICAgICByZXR1cm4gY2xlYW51cDtcbiAgICB9KTtcbiAgICBpZiAoaGFzQ2xlYW51cCkge1xuICAgICAgcmV0dXJuICgpID0+IHtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjbGVhbnVwcy5sZW5ndGg7IGkrKykge1xuICAgICAgICAgIGNvbnN0IGNsZWFudXAgPSBjbGVhbnVwc1tpXTtcbiAgICAgICAgICBpZiAodHlwZW9mIGNsZWFudXAgPT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICBjbGVhbnVwKCk7XG4gICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIHNldFJlZihyZWZzW2ldLCBudWxsKTtcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH07XG4gICAgfVxuICB9O1xufVxuZnVuY3Rpb24gdXNlQ29tcG9zZWRSZWZzKC4uLnJlZnMpIHtcbiAgcmV0dXJuIFJlYWN0LnVzZUNhbGxiYWNrKGNvbXBvc2VSZWZzKC4uLnJlZnMpLCByZWZzKTtcbn1cbmV4cG9ydCB7XG4gIGNvbXBvc2VSZWZzLFxuICB1c2VDb21wb3NlZFJlZnNcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs":
/*!*************************************************************!*\
  !*** ./node_modules/@radix-ui/react-context/dist/index.mjs ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createContext: () => (/* binding */ createContext2),\n/* harmony export */   createContextScope: () => (/* binding */ createContextScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/context/src/createContext.tsx\n\n\nfunction createContext2(rootComponentName, defaultContext) {\n  const Context = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n  const Provider = (props) => {\n    const { children, ...context } = props;\n    const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n  };\n  Provider.displayName = rootComponentName + \"Provider\";\n  function useContext2(consumerName) {\n    const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== void 0) return defaultContext;\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n  return [Provider, useContext2];\n}\nfunction createContextScope(scopeName, createContextScopeDeps = []) {\n  let defaultContexts = [];\n  function createContext3(rootComponentName, defaultContext) {\n    const BaseContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n    const Provider = (props) => {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const value = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => context, Object.values(context));\n      return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Context.Provider, { value, children });\n    };\n    Provider.displayName = rootComponentName + \"Provider\";\n    function useContext2(consumerName, scope) {\n      const Context = scope?.[scopeName]?.[index] || BaseContext;\n      const context = react__WEBPACK_IMPORTED_MODULE_0__.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== void 0) return defaultContext;\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n    return [Provider, useContext2];\n  }\n  const createScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return react__WEBPACK_IMPORTED_MODULE_0__.createContext(defaultContext);\n    });\n    return function useScope(scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n  createScope.scopeName = scopeName;\n  return [createContext3, composeContextScopes(createScope, ...createContextScopeDeps)];\n}\nfunction composeContextScopes(...scopes) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n  const createScope = () => {\n    const scopeHooks = scopes.map((createScope2) => ({\n      useScope: createScope2(),\n      scopeName: createScope2.scopeName\n    }));\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes2, { useScope, scopeName }) => {\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes2, ...currentScope };\n      }, {});\n      return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-direction/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DirectionProvider: () => (/* binding */ DirectionProvider),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   useDirection: () => (/* binding */ useDirection)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/direction/src/Direction.tsx\n\n\nvar DirectionContext = react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0);\nvar DirectionProvider = (props) => {\n  const { dir, children } = props;\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DirectionContext.Provider, { value: dir, children });\n};\nfunction useDirection(localDir) {\n  const globalDir = react__WEBPACK_IMPORTED_MODULE_0__.useContext(DirectionContext);\n  return localDir || globalDir || \"ltr\";\n}\nvar Provider = DirectionProvider;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LWRpcmVjdGlvbi9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBO0FBQytCO0FBQ1M7QUFDeEMsdUJBQXVCLGdEQUFtQjtBQUMxQztBQUNBLFVBQVUsZ0JBQWdCO0FBQzFCLHlCQUF5QixzREFBRyw4QkFBOEIsc0JBQXNCO0FBQ2hGO0FBQ0E7QUFDQSxvQkFBb0IsNkNBQWdCO0FBQ3BDO0FBQ0E7QUFDQTtBQUtFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC1kaXJlY3Rpb24vZGlzdC9pbmRleC5tanM/ODk0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWNrYWdlcy9yZWFjdC9kaXJlY3Rpb24vc3JjL0RpcmVjdGlvbi50c3hcbmltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsganN4IH0gZnJvbSBcInJlYWN0L2pzeC1ydW50aW1lXCI7XG52YXIgRGlyZWN0aW9uQ29udGV4dCA9IFJlYWN0LmNyZWF0ZUNvbnRleHQodm9pZCAwKTtcbnZhciBEaXJlY3Rpb25Qcm92aWRlciA9IChwcm9wcykgPT4ge1xuICBjb25zdCB7IGRpciwgY2hpbGRyZW4gfSA9IHByb3BzO1xuICByZXR1cm4gLyogQF9fUFVSRV9fICovIGpzeChEaXJlY3Rpb25Db250ZXh0LlByb3ZpZGVyLCB7IHZhbHVlOiBkaXIsIGNoaWxkcmVuIH0pO1xufTtcbmZ1bmN0aW9uIHVzZURpcmVjdGlvbihsb2NhbERpcikge1xuICBjb25zdCBnbG9iYWxEaXIgPSBSZWFjdC51c2VDb250ZXh0KERpcmVjdGlvbkNvbnRleHQpO1xuICByZXR1cm4gbG9jYWxEaXIgfHwgZ2xvYmFsRGlyIHx8IFwibHRyXCI7XG59XG52YXIgUHJvdmlkZXIgPSBEaXJlY3Rpb25Qcm92aWRlcjtcbmV4cG9ydCB7XG4gIERpcmVjdGlvblByb3ZpZGVyLFxuICBQcm92aWRlcixcbiAgdXNlRGlyZWN0aW9uXG59O1xuLy8jIHNvdXJjZU1hcHBpbmdVUkw9aW5kZXgubWpzLm1hcFxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs":
/*!**************************************************************!*\
  !*** ./node_modules/@radix-ui/react-presence/dist/index.mjs ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Presence: () => (/* binding */ Presence)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ Presence auto */ // packages/react/presence/src/Presence.tsx\n\n\n\n// packages/react/presence/src/useStateMachine.tsx\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// packages/react/presence/src/Presence.tsx\nvar Presence = (props)=>{\n    const { present, children } = props;\n    const presence = usePresence(present);\n    const child = typeof children === \"function\" ? children({\n        present: presence.isPresent\n    }) : react__WEBPACK_IMPORTED_MODULE_0__.Children.only(children);\n    const ref = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_1__.useComposedRefs)(presence.ref, getElementRef(child));\n    const forceMount = typeof children === \"function\";\n    return forceMount || presence.isPresent ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(child, {\n        ref\n    }) : null;\n};\nPresence.displayName = \"Presence\";\nfunction usePresence(present) {\n    const [node, setNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const stylesRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef({});\n    const prevPresentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(present);\n    const prevAnimationNameRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"none\");\n    const initialState = present ? \"mounted\" : \"unmounted\";\n    const [state, send] = useStateMachine(initialState, {\n        mounted: {\n            UNMOUNT: \"unmounted\",\n            ANIMATION_OUT: \"unmountSuspended\"\n        },\n        unmountSuspended: {\n            MOUNT: \"mounted\",\n            ANIMATION_END: \"unmounted\"\n        },\n        unmounted: {\n            MOUNT: \"mounted\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const currentAnimationName = getAnimationName(stylesRef.current);\n        prevAnimationNameRef.current = state === \"mounted\" ? currentAnimationName : \"none\";\n    }, [\n        state\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        const styles = stylesRef.current;\n        const wasPresent = prevPresentRef.current;\n        const hasPresentChanged = wasPresent !== present;\n        if (hasPresentChanged) {\n            const prevAnimationName = prevAnimationNameRef.current;\n            const currentAnimationName = getAnimationName(styles);\n            if (present) {\n                send(\"MOUNT\");\n            } else if (currentAnimationName === \"none\" || styles?.display === \"none\") {\n                send(\"UNMOUNT\");\n            } else {\n                const isAnimating = prevAnimationName !== currentAnimationName;\n                if (wasPresent && isAnimating) {\n                    send(\"ANIMATION_OUT\");\n                } else {\n                    send(\"UNMOUNT\");\n                }\n            }\n            prevPresentRef.current = present;\n        }\n    }, [\n        present,\n        send\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_2__.useLayoutEffect)(()=>{\n        if (node) {\n            let timeoutId;\n            const ownerWindow = node.ownerDocument.defaultView ?? window;\n            const handleAnimationEnd = (event)=>{\n                const currentAnimationName = getAnimationName(stylesRef.current);\n                const isCurrentAnimation = currentAnimationName.includes(event.animationName);\n                if (event.target === node && isCurrentAnimation) {\n                    send(\"ANIMATION_END\");\n                    if (!prevPresentRef.current) {\n                        const currentFillMode = node.style.animationFillMode;\n                        node.style.animationFillMode = \"forwards\";\n                        timeoutId = ownerWindow.setTimeout(()=>{\n                            if (node.style.animationFillMode === \"forwards\") {\n                                node.style.animationFillMode = currentFillMode;\n                            }\n                        });\n                    }\n                }\n            };\n            const handleAnimationStart = (event)=>{\n                if (event.target === node) {\n                    prevAnimationNameRef.current = getAnimationName(stylesRef.current);\n                }\n            };\n            node.addEventListener(\"animationstart\", handleAnimationStart);\n            node.addEventListener(\"animationcancel\", handleAnimationEnd);\n            node.addEventListener(\"animationend\", handleAnimationEnd);\n            return ()=>{\n                ownerWindow.clearTimeout(timeoutId);\n                node.removeEventListener(\"animationstart\", handleAnimationStart);\n                node.removeEventListener(\"animationcancel\", handleAnimationEnd);\n                node.removeEventListener(\"animationend\", handleAnimationEnd);\n            };\n        } else {\n            send(\"ANIMATION_END\");\n        }\n    }, [\n        node,\n        send\n    ]);\n    return {\n        isPresent: [\n            \"mounted\",\n            \"unmountSuspended\"\n        ].includes(state),\n        ref: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node2)=>{\n            if (node2) stylesRef.current = getComputedStyle(node2);\n            setNode(node2);\n        }, [])\n    };\n}\nfunction getAnimationName(styles) {\n    return styles?.animationName || \"none\";\n}\nfunction getElementRef(element) {\n    let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n    let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.ref;\n    }\n    getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n    mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n    if (mayWarn) {\n        return element.props.ref;\n    }\n    return element.props.ref || element.ref;\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs":
/*!***************************************************************!*\
  !*** ./node_modules/@radix-ui/react-primitive/dist/index.mjs ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Primitive: () => (/* binding */ Primitive),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   dispatchDiscreteCustomEvent: () => (/* binding */ dispatchDiscreteCustomEvent)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/primitive/src/Primitive.tsx\n\n\n\n\nvar NODES = [\n  \"a\",\n  \"button\",\n  \"div\",\n  \"form\",\n  \"h2\",\n  \"h3\",\n  \"img\",\n  \"input\",\n  \"label\",\n  \"li\",\n  \"nav\",\n  \"ol\",\n  \"p\",\n  \"span\",\n  \"svg\",\n  \"ul\"\n];\nvar Primitive = NODES.reduce((primitive, node) => {\n  const Node = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n    const { asChild, ...primitiveProps } = props;\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_3__.Slot : node;\n    if (typeof window !== \"undefined\") {\n      window[Symbol.for(\"radix-ui\")] = true;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Comp, { ...primitiveProps, ref: forwardedRef });\n  });\n  Node.displayName = `Primitive.${node}`;\n  return { ...primitive, [node]: Node };\n}, {});\nfunction dispatchDiscreteCustomEvent(target, event) {\n  if (target) react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync(() => target.dispatchEvent(event));\n}\nvar Root = Primitive;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQUE7QUFDK0I7QUFDTztBQUNNO0FBQ0o7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxlQUFlLDZDQUFnQjtBQUMvQixZQUFZLDZCQUE2QjtBQUN6QywyQkFBMkIsc0RBQUk7QUFDL0I7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLHNEQUFHLFNBQVMsc0NBQXNDO0FBQzdFLEdBQUc7QUFDSCxrQ0FBa0MsS0FBSztBQUN2QyxXQUFXO0FBQ1gsQ0FBQyxJQUFJO0FBQ0w7QUFDQSxjQUFjLGdEQUFrQjtBQUNoQztBQUNBO0FBS0U7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXByaW1pdGl2ZS9kaXN0L2luZGV4Lm1qcz83ZjJmIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3ByaW1pdGl2ZS9zcmMvUHJpbWl0aXZlLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgKiBhcyBSZWFjdERPTSBmcm9tIFwicmVhY3QtZG9tXCI7XG5pbXBvcnQgeyBTbG90IH0gZnJvbSBcIkByYWRpeC11aS9yZWFjdC1zbG90XCI7XG5pbXBvcnQgeyBqc3ggfSBmcm9tIFwicmVhY3QvanN4LXJ1bnRpbWVcIjtcbnZhciBOT0RFUyA9IFtcbiAgXCJhXCIsXG4gIFwiYnV0dG9uXCIsXG4gIFwiZGl2XCIsXG4gIFwiZm9ybVwiLFxuICBcImgyXCIsXG4gIFwiaDNcIixcbiAgXCJpbWdcIixcbiAgXCJpbnB1dFwiLFxuICBcImxhYmVsXCIsXG4gIFwibGlcIixcbiAgXCJuYXZcIixcbiAgXCJvbFwiLFxuICBcInBcIixcbiAgXCJzcGFuXCIsXG4gIFwic3ZnXCIsXG4gIFwidWxcIlxuXTtcbnZhciBQcmltaXRpdmUgPSBOT0RFUy5yZWR1Y2UoKHByaW1pdGl2ZSwgbm9kZSkgPT4ge1xuICBjb25zdCBOb2RlID0gUmVhY3QuZm9yd2FyZFJlZigocHJvcHMsIGZvcndhcmRlZFJlZikgPT4ge1xuICAgIGNvbnN0IHsgYXNDaGlsZCwgLi4ucHJpbWl0aXZlUHJvcHMgfSA9IHByb3BzO1xuICAgIGNvbnN0IENvbXAgPSBhc0NoaWxkID8gU2xvdCA6IG5vZGU7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICAgIHdpbmRvd1tTeW1ib2wuZm9yKFwicmFkaXgtdWlcIildID0gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIC8qIEBfX1BVUkVfXyAqLyBqc3goQ29tcCwgeyAuLi5wcmltaXRpdmVQcm9wcywgcmVmOiBmb3J3YXJkZWRSZWYgfSk7XG4gIH0pO1xuICBOb2RlLmRpc3BsYXlOYW1lID0gYFByaW1pdGl2ZS4ke25vZGV9YDtcbiAgcmV0dXJuIHsgLi4ucHJpbWl0aXZlLCBbbm9kZV06IE5vZGUgfTtcbn0sIHt9KTtcbmZ1bmN0aW9uIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudCh0YXJnZXQsIGV2ZW50KSB7XG4gIGlmICh0YXJnZXQpIFJlYWN0RE9NLmZsdXNoU3luYygoKSA9PiB0YXJnZXQuZGlzcGF0Y2hFdmVudChldmVudCkpO1xufVxudmFyIFJvb3QgPSBQcmltaXRpdmU7XG5leHBvcnQge1xuICBQcmltaXRpdmUsXG4gIFJvb3QsXG4gIGRpc3BhdGNoRGlzY3JldGVDdXN0b21FdmVudFxufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPWluZGV4Lm1qcy5tYXBcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@radix-ui/react-scroll-area/dist/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Corner: () => (/* binding */ Corner),\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   ScrollArea: () => (/* binding */ ScrollArea),\n/* harmony export */   ScrollAreaCorner: () => (/* binding */ ScrollAreaCorner),\n/* harmony export */   ScrollAreaScrollbar: () => (/* binding */ ScrollAreaScrollbar),\n/* harmony export */   ScrollAreaThumb: () => (/* binding */ ScrollAreaThumb),\n/* harmony export */   ScrollAreaViewport: () => (/* binding */ ScrollAreaViewport),\n/* harmony export */   Scrollbar: () => (/* binding */ Scrollbar),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createScrollAreaScope: () => (/* binding */ createScrollAreaScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Corner,Root,ScrollArea,ScrollAreaCorner,ScrollAreaScrollbar,ScrollAreaThumb,ScrollAreaViewport,Scrollbar,Thumb,Viewport,createScrollAreaScope auto */ // packages/react/scroll-area/src/ScrollArea.tsx\n\n\n\n\n\n\n\n\n\n\n// packages/react/scroll-area/src/useStateMachine.ts\n\nfunction useStateMachine(initialState, machine) {\n    return react__WEBPACK_IMPORTED_MODULE_0__.useReducer((state, event)=>{\n        const nextState = machine[state][event];\n        return nextState ?? state;\n    }, initialState);\n}\n// packages/react/scroll-area/src/ScrollArea.tsx\n\nvar SCROLL_AREA_NAME = \"ScrollArea\";\nvar [createScrollAreaContext, createScrollAreaScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SCROLL_AREA_NAME);\nvar [ScrollAreaProvider, useScrollAreaContext] = createScrollAreaContext(SCROLL_AREA_NAME);\nvar ScrollArea = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, type = \"hover\", dir, scrollHideDelay = 600, ...scrollAreaProps } = props;\n    const [scrollArea, setScrollArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarX, setScrollbarX] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [scrollbarY, setScrollbarY] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [cornerWidth, setCornerWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [cornerHeight, setCornerHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [scrollbarXEnabled, setScrollbarXEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [scrollbarYEnabled, setScrollbarYEnabled] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollArea(node));\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_4__.useDirection)(dir);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaProvider, {\n        scope: __scopeScrollArea,\n        type,\n        dir: direction,\n        scrollHideDelay,\n        scrollArea,\n        viewport,\n        onViewportChange: setViewport,\n        content,\n        onContentChange: setContent,\n        scrollbarX,\n        onScrollbarXChange: setScrollbarX,\n        scrollbarXEnabled,\n        onScrollbarXEnabledChange: setScrollbarXEnabled,\n        scrollbarY,\n        onScrollbarYChange: setScrollbarY,\n        scrollbarYEnabled,\n        onScrollbarYEnabledChange: setScrollbarYEnabled,\n        onCornerWidthChange: setCornerWidth,\n        onCornerHeightChange: setCornerHeight,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            dir: direction,\n            ...scrollAreaProps,\n            ref: composedRefs,\n            style: {\n                position: \"relative\",\n                // Pass corner sizes as CSS vars to reduce re-renders of context consumers\n                [\"--radix-scroll-area-corner-width\"]: cornerWidth + \"px\",\n                [\"--radix-scroll-area-corner-height\"]: cornerHeight + \"px\",\n                ...props.style\n            }\n        })\n    });\n});\nScrollArea.displayName = SCROLL_AREA_NAME;\nvar VIEWPORT_NAME = \"ScrollAreaViewport\";\nvar ScrollAreaViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, children, nonce, ...viewportProps } = props;\n    const context = useScrollAreaContext(VIEWPORT_NAME, __scopeScrollArea);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onViewportChange);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n                \"data-radix-scroll-area-viewport\": \"\",\n                ...viewportProps,\n                ref: composedRefs,\n                style: {\n                    /**\n             * We don't support `visible` because the intention is to have at least one scrollbar\n             * if this component is used and `visible` will behave like `auto` in that case\n             * https://developer.mozilla.org/en-US/docs/Web/CSS/overflow#description\n             *\n             * We don't handle `auto` because the intention is for the native implementation\n             * to be hidden if using this component. We just want to ensure the node is scrollable\n             * so could have used either `scroll` or `auto` here. We picked `scroll` to prevent\n             * the browser from having to work out whether to render native scrollbars or not,\n             * we tell it to with the intention of hiding them in CSS.\n             */ overflowX: context.scrollbarXEnabled ? \"scroll\" : \"hidden\",\n                    overflowY: context.scrollbarYEnabled ? \"scroll\" : \"hidden\",\n                    ...props.style\n                },\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n                    ref: context.onContentChange,\n                    style: {\n                        minWidth: \"100%\",\n                        display: \"table\"\n                    },\n                    children\n                })\n            })\n        ]\n    });\n});\nScrollAreaViewport.displayName = VIEWPORT_NAME;\nvar SCROLLBAR_NAME = \"ScrollAreaScrollbar\";\nvar ScrollAreaScrollbar = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { onScrollbarXEnabledChange, onScrollbarYEnabledChange } = context;\n    const isHorizontal = props.orientation === \"horizontal\";\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        isHorizontal ? onScrollbarXEnabledChange(true) : onScrollbarYEnabledChange(true);\n        return ()=>{\n            isHorizontal ? onScrollbarXEnabledChange(false) : onScrollbarYEnabledChange(false);\n        };\n    }, [\n        isHorizontal,\n        onScrollbarXEnabledChange,\n        onScrollbarYEnabledChange\n    ]);\n    return context.type === \"hover\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarHover, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"scroll\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarScroll, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"auto\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n        ...scrollbarProps,\n        ref: forwardedRef,\n        forceMount\n    }) : context.type === \"always\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n        ...scrollbarProps,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaScrollbar.displayName = SCROLLBAR_NAME;\nvar ScrollAreaScrollbarHover = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const scrollArea = context.scrollArea;\n        let hideTimer = 0;\n        if (scrollArea) {\n            const handlePointerEnter = ()=>{\n                window.clearTimeout(hideTimer);\n                setVisible(true);\n            };\n            const handlePointerLeave = ()=>{\n                hideTimer = window.setTimeout(()=>setVisible(false), context.scrollHideDelay);\n            };\n            scrollArea.addEventListener(\"pointerenter\", handlePointerEnter);\n            scrollArea.addEventListener(\"pointerleave\", handlePointerLeave);\n            return ()=>{\n                window.clearTimeout(hideTimer);\n                scrollArea.removeEventListener(\"pointerenter\", handlePointerEnter);\n                scrollArea.removeEventListener(\"pointerleave\", handlePointerLeave);\n            };\n        }\n    }, [\n        context.scrollArea,\n        context.scrollHideDelay\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarAuto, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarScroll = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const debounceScrollEnd = useDebounceCallback(()=>send(\"SCROLL_END\"), 100);\n    const [state, send] = useStateMachine(\"hidden\", {\n        hidden: {\n            SCROLL: \"scrolling\"\n        },\n        scrolling: {\n            SCROLL_END: \"idle\",\n            POINTER_ENTER: \"interacting\"\n        },\n        interacting: {\n            SCROLL: \"interacting\",\n            POINTER_LEAVE: \"idle\"\n        },\n        idle: {\n            HIDE: \"hidden\",\n            SCROLL: \"scrolling\",\n            POINTER_ENTER: \"interacting\"\n        }\n    });\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (state === \"idle\") {\n            const hideTimer = window.setTimeout(()=>send(\"HIDE\"), context.scrollHideDelay);\n            return ()=>window.clearTimeout(hideTimer);\n        }\n    }, [\n        state,\n        context.scrollHideDelay,\n        send\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = context.viewport;\n        const scrollDirection = isHorizontal ? \"scrollLeft\" : \"scrollTop\";\n        if (viewport) {\n            let prevScrollPos = viewport[scrollDirection];\n            const handleScroll = ()=>{\n                const scrollPos = viewport[scrollDirection];\n                const hasScrollInDirectionChanged = prevScrollPos !== scrollPos;\n                if (hasScrollInDirectionChanged) {\n                    send(\"SCROLL\");\n                    debounceScrollEnd();\n                }\n                prevScrollPos = scrollPos;\n            };\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        context.viewport,\n        isHorizontal,\n        send,\n        debounceScrollEnd\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || state !== \"hidden\",\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": state === \"hidden\" ? \"hidden\" : \"visible\",\n            ...scrollbarProps,\n            ref: forwardedRef,\n            onPointerEnter: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerEnter, ()=>send(\"POINTER_ENTER\")),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerLeave, ()=>send(\"POINTER_LEAVE\"))\n        })\n    });\n});\nvar ScrollAreaScrollbarAuto = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const { forceMount, ...scrollbarProps } = props;\n    const [visible, setVisible] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const isHorizontal = props.orientation === \"horizontal\";\n    const handleResize = useDebounceCallback(()=>{\n        if (context.viewport) {\n            const isOverflowX = context.viewport.offsetWidth < context.viewport.scrollWidth;\n            const isOverflowY = context.viewport.offsetHeight < context.viewport.scrollHeight;\n            setVisible(isHorizontal ? isOverflowX : isOverflowY);\n        }\n    }, 10);\n    useResizeObserver(context.viewport, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || visible,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarVisible, {\n            \"data-state\": visible ? \"visible\" : \"hidden\",\n            ...scrollbarProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar ScrollAreaScrollbarVisible = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { orientation = \"vertical\", ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const thumbRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const pointerOffsetRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const [sizes, setSizes] = react__WEBPACK_IMPORTED_MODULE_0__.useState({\n        content: 0,\n        viewport: 0,\n        scrollbar: {\n            size: 0,\n            paddingStart: 0,\n            paddingEnd: 0\n        }\n    });\n    const thumbRatio = getThumbRatio(sizes.viewport, sizes.content);\n    const commonProps = {\n        ...scrollbarProps,\n        sizes,\n        onSizesChange: setSizes,\n        hasThumb: Boolean(thumbRatio > 0 && thumbRatio < 1),\n        onThumbChange: (thumb)=>thumbRef.current = thumb,\n        onThumbPointerUp: ()=>pointerOffsetRef.current = 0,\n        onThumbPointerDown: (pointerPos)=>pointerOffsetRef.current = pointerPos\n    };\n    function getScrollPosition(pointerPos, dir) {\n        return getScrollPositionFromPointer(pointerPos, pointerOffsetRef.current, sizes, dir);\n    }\n    if (orientation === \"horizontal\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarX, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollLeft;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes, context.dir);\n                    thumbRef.current.style.transform = `translate3d(${offset}px, 0, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollLeft = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) {\n                    context.viewport.scrollLeft = getScrollPosition(pointerPos, context.dir);\n                }\n            }\n        });\n    }\n    if (orientation === \"vertical\") {\n        return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarY, {\n            ...commonProps,\n            ref: forwardedRef,\n            onThumbPositionChange: ()=>{\n                if (context.viewport && thumbRef.current) {\n                    const scrollPos = context.viewport.scrollTop;\n                    const offset = getThumbOffsetFromScroll(scrollPos, sizes);\n                    thumbRef.current.style.transform = `translate3d(0, ${offset}px, 0)`;\n                }\n            },\n            onWheelScroll: (scrollPos)=>{\n                if (context.viewport) context.viewport.scrollTop = scrollPos;\n            },\n            onDragScroll: (pointerPos)=>{\n                if (context.viewport) context.viewport.scrollTop = getScrollPosition(pointerPos);\n            }\n        });\n    }\n    return null;\n});\nvar ScrollAreaScrollbarX = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarXChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"horizontal\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            bottom: 0,\n            left: context.dir === \"rtl\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            right: context.dir === \"ltr\" ? \"var(--radix-scroll-area-corner-width)\" : 0,\n            [\"--radix-scroll-area-thumb-width\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.x),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.x),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollLeft + event.deltaX;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollWidth,\n                    viewport: context.viewport.offsetWidth,\n                    scrollbar: {\n                        size: ref.current.clientWidth,\n                        paddingStart: toInt(computedStyle.paddingLeft),\n                        paddingEnd: toInt(computedStyle.paddingRight)\n                    }\n                });\n            }\n        }\n    });\n});\nvar ScrollAreaScrollbarY = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { sizes, onSizesChange, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, props.__scopeScrollArea);\n    const [computedStyle, setComputedStyle] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref, context.onScrollbarYChange);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (ref.current) setComputedStyle(getComputedStyle(ref.current));\n    }, [\n        ref\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaScrollbarImpl, {\n        \"data-orientation\": \"vertical\",\n        ...scrollbarProps,\n        ref: composeRefs,\n        sizes,\n        style: {\n            top: 0,\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: \"var(--radix-scroll-area-corner-height)\",\n            [\"--radix-scroll-area-thumb-height\"]: getThumbSize(sizes) + \"px\",\n            ...props.style\n        },\n        onThumbPointerDown: (pointerPos)=>props.onThumbPointerDown(pointerPos.y),\n        onDragScroll: (pointerPos)=>props.onDragScroll(pointerPos.y),\n        onWheelScroll: (event, maxScrollPos)=>{\n            if (context.viewport) {\n                const scrollPos = context.viewport.scrollTop + event.deltaY;\n                props.onWheelScroll(scrollPos);\n                if (isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos)) {\n                    event.preventDefault();\n                }\n            }\n        },\n        onResize: ()=>{\n            if (ref.current && context.viewport && computedStyle) {\n                onSizesChange({\n                    content: context.viewport.scrollHeight,\n                    viewport: context.viewport.offsetHeight,\n                    scrollbar: {\n                        size: ref.current.clientHeight,\n                        paddingStart: toInt(computedStyle.paddingTop),\n                        paddingEnd: toInt(computedStyle.paddingBottom)\n                    }\n                });\n            }\n        }\n    });\n});\nvar [ScrollbarProvider, useScrollbarContext] = createScrollAreaContext(SCROLLBAR_NAME);\nvar ScrollAreaScrollbarImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, sizes, hasThumb, onThumbChange, onThumbPointerUp, onThumbPointerDown, onThumbPositionChange, onDragScroll, onWheelScroll, onResize, ...scrollbarProps } = props;\n    const context = useScrollAreaContext(SCROLLBAR_NAME, __scopeScrollArea);\n    const [scrollbar, setScrollbar] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composeRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setScrollbar(node));\n    const rectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const prevWebkitUserSelectRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const viewport = context.viewport;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const handleWheelScroll = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onWheelScroll);\n    const handleThumbPositionChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPositionChange);\n    const handleResize = useDebounceCallback(onResize, 10);\n    function handleDragScroll(event) {\n        if (rectRef.current) {\n            const x = event.clientX - rectRef.current.left;\n            const y = event.clientY - rectRef.current.top;\n            onDragScroll({\n                x,\n                y\n            });\n        }\n    }\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const handleWheel = (event)=>{\n            const element = event.target;\n            const isScrollbarWheel = scrollbar?.contains(element);\n            if (isScrollbarWheel) handleWheelScroll(event, maxScrollPos);\n        };\n        document.addEventListener(\"wheel\", handleWheel, {\n            passive: false\n        });\n        return ()=>document.removeEventListener(\"wheel\", handleWheel, {\n                passive: false\n            });\n    }, [\n        viewport,\n        scrollbar,\n        maxScrollPos,\n        handleWheelScroll\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(handleThumbPositionChange, [\n        sizes,\n        handleThumbPositionChange\n    ]);\n    useResizeObserver(scrollbar, handleResize);\n    useResizeObserver(context.content, handleResize);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollbarProvider, {\n        scope: __scopeScrollArea,\n        scrollbar,\n        hasThumb,\n        onThumbChange: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbChange),\n        onThumbPointerUp: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerUp),\n        onThumbPositionChange: handleThumbPositionChange,\n        onThumbPointerDown: (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onThumbPointerDown),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n            ...scrollbarProps,\n            ref: composeRefs,\n            style: {\n                position: \"absolute\",\n                ...scrollbarProps.style\n            },\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                const mainPointer = 0;\n                if (event.button === mainPointer) {\n                    const element = event.target;\n                    element.setPointerCapture(event.pointerId);\n                    rectRef.current = scrollbar.getBoundingClientRect();\n                    prevWebkitUserSelectRef.current = document.body.style.webkitUserSelect;\n                    document.body.style.webkitUserSelect = \"none\";\n                    if (context.viewport) context.viewport.style.scrollBehavior = \"auto\";\n                    handleDragScroll(event);\n                }\n            }),\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerMove, handleDragScroll),\n            onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, (event)=>{\n                const element = event.target;\n                if (element.hasPointerCapture(event.pointerId)) {\n                    element.releasePointerCapture(event.pointerId);\n                }\n                document.body.style.webkitUserSelect = prevWebkitUserSelectRef.current;\n                if (context.viewport) context.viewport.style.scrollBehavior = \"\";\n                rectRef.current = null;\n            })\n        })\n    });\n});\nvar THUMB_NAME = \"ScrollAreaThumb\";\nvar ScrollAreaThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { forceMount, ...thumbProps } = props;\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, props.__scopeScrollArea);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_6__.Presence, {\n        present: forceMount || scrollbarContext.hasThumb,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaThumbImpl, {\n            ref: forwardedRef,\n            ...thumbProps\n        })\n    });\n});\nvar ScrollAreaThumbImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, style, ...thumbProps } = props;\n    const scrollAreaContext = useScrollAreaContext(THUMB_NAME, __scopeScrollArea);\n    const scrollbarContext = useScrollbarContext(THUMB_NAME, __scopeScrollArea);\n    const { onThumbPositionChange } = scrollbarContext;\n    const composedRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>scrollbarContext.onThumbChange(node));\n    const removeUnlinkedScrollListenerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(void 0);\n    const debounceScrollEnd = useDebounceCallback(()=>{\n        if (removeUnlinkedScrollListenerRef.current) {\n            removeUnlinkedScrollListenerRef.current();\n            removeUnlinkedScrollListenerRef.current = void 0;\n        }\n    }, 100);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const viewport = scrollAreaContext.viewport;\n        if (viewport) {\n            const handleScroll = ()=>{\n                debounceScrollEnd();\n                if (!removeUnlinkedScrollListenerRef.current) {\n                    const listener = addUnlinkedScrollListener(viewport, onThumbPositionChange);\n                    removeUnlinkedScrollListenerRef.current = listener;\n                    onThumbPositionChange();\n                }\n            };\n            onThumbPositionChange();\n            viewport.addEventListener(\"scroll\", handleScroll);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll);\n        }\n    }, [\n        scrollAreaContext.viewport,\n        debounceScrollEnd,\n        onThumbPositionChange\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        \"data-state\": scrollbarContext.hasThumb ? \"visible\" : \"hidden\",\n        ...thumbProps,\n        ref: composedRef,\n        style: {\n            width: \"var(--radix-scroll-area-thumb-width)\",\n            height: \"var(--radix-scroll-area-thumb-height)\",\n            ...style\n        },\n        onPointerDownCapture: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerDownCapture, (event)=>{\n            const thumb = event.target;\n            const thumbRect = thumb.getBoundingClientRect();\n            const x = event.clientX - thumbRect.left;\n            const y = event.clientY - thumbRect.top;\n            scrollbarContext.onThumbPointerDown({\n                x,\n                y\n            });\n        }),\n        onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_7__.composeEventHandlers)(props.onPointerUp, scrollbarContext.onThumbPointerUp)\n    });\n});\nScrollAreaThumb.displayName = THUMB_NAME;\nvar CORNER_NAME = \"ScrollAreaCorner\";\nvar ScrollAreaCorner = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useScrollAreaContext(CORNER_NAME, props.__scopeScrollArea);\n    const hasBothScrollbarsVisible = Boolean(context.scrollbarX && context.scrollbarY);\n    const hasCorner = context.type !== \"scroll\" && hasBothScrollbarsVisible;\n    return hasCorner ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ScrollAreaCornerImpl, {\n        ...props,\n        ref: forwardedRef\n    }) : null;\n});\nScrollAreaCorner.displayName = CORNER_NAME;\nvar ScrollAreaCornerImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeScrollArea, ...cornerProps } = props;\n    const context = useScrollAreaContext(CORNER_NAME, __scopeScrollArea);\n    const [width, setWidth] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [height, setHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const hasSize = Boolean(width && height);\n    useResizeObserver(context.scrollbarX, ()=>{\n        const height2 = context.scrollbarX?.offsetHeight || 0;\n        context.onCornerHeightChange(height2);\n        setHeight(height2);\n    });\n    useResizeObserver(context.scrollbarY, ()=>{\n        const width2 = context.scrollbarY?.offsetWidth || 0;\n        context.onCornerWidthChange(width2);\n        setWidth(width2);\n    });\n    return hasSize ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.div, {\n        ...cornerProps,\n        ref: forwardedRef,\n        style: {\n            width,\n            height,\n            position: \"absolute\",\n            right: context.dir === \"ltr\" ? 0 : void 0,\n            left: context.dir === \"rtl\" ? 0 : void 0,\n            bottom: 0,\n            ...props.style\n        }\n    }) : null;\n});\nfunction toInt(value) {\n    return value ? parseInt(value, 10) : 0;\n}\nfunction getThumbRatio(viewportSize, contentSize) {\n    const ratio = viewportSize / contentSize;\n    return isNaN(ratio) ? 0 : ratio;\n}\nfunction getThumbSize(sizes) {\n    const ratio = getThumbRatio(sizes.viewport, sizes.content);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const thumbSize = (sizes.scrollbar.size - scrollbarPadding) * ratio;\n    return Math.max(thumbSize, 18);\n}\nfunction getScrollPositionFromPointer(pointerPos, pointerOffset, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const thumbCenter = thumbSizePx / 2;\n    const offset = pointerOffset || thumbCenter;\n    const thumbOffsetFromEnd = thumbSizePx - offset;\n    const minPointerPos = sizes.scrollbar.paddingStart + offset;\n    const maxPointerPos = sizes.scrollbar.size - sizes.scrollbar.paddingEnd - thumbOffsetFromEnd;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const scrollRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const interpolate = linearScale([\n        minPointerPos,\n        maxPointerPos\n    ], scrollRange);\n    return interpolate(pointerPos);\n}\nfunction getThumbOffsetFromScroll(scrollPos, sizes, dir = \"ltr\") {\n    const thumbSizePx = getThumbSize(sizes);\n    const scrollbarPadding = sizes.scrollbar.paddingStart + sizes.scrollbar.paddingEnd;\n    const scrollbar = sizes.scrollbar.size - scrollbarPadding;\n    const maxScrollPos = sizes.content - sizes.viewport;\n    const maxThumbPos = scrollbar - thumbSizePx;\n    const scrollClampRange = dir === \"ltr\" ? [\n        0,\n        maxScrollPos\n    ] : [\n        maxScrollPos * -1,\n        0\n    ];\n    const scrollWithoutMomentum = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_9__.clamp)(scrollPos, scrollClampRange);\n    const interpolate = linearScale([\n        0,\n        maxScrollPos\n    ], [\n        0,\n        maxThumbPos\n    ]);\n    return interpolate(scrollWithoutMomentum);\n}\nfunction linearScale(input, output) {\n    return (value)=>{\n        if (input[0] === input[1] || output[0] === output[1]) return output[0];\n        const ratio = (output[1] - output[0]) / (input[1] - input[0]);\n        return output[0] + ratio * (value - input[0]);\n    };\n}\nfunction isScrollingWithinScrollbarBounds(scrollPos, maxScrollPos) {\n    return scrollPos > 0 && scrollPos < maxScrollPos;\n}\nvar addUnlinkedScrollListener = (node, handler = ()=>{})=>{\n    let prevPosition = {\n        left: node.scrollLeft,\n        top: node.scrollTop\n    };\n    let rAF = 0;\n    (function loop() {\n        const position = {\n            left: node.scrollLeft,\n            top: node.scrollTop\n        };\n        const isHorizontalScroll = prevPosition.left !== position.left;\n        const isVerticalScroll = prevPosition.top !== position.top;\n        if (isHorizontalScroll || isVerticalScroll) handler();\n        prevPosition = position;\n        rAF = window.requestAnimationFrame(loop);\n    })();\n    return ()=>window.cancelAnimationFrame(rAF);\n};\nfunction useDebounceCallback(callback, delay) {\n    const handleCallback = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(callback);\n    const debounceTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>()=>window.clearTimeout(debounceTimerRef.current), []);\n    return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(debounceTimerRef.current);\n        debounceTimerRef.current = window.setTimeout(handleCallback, delay);\n    }, [\n        handleCallback,\n        delay\n    ]);\n}\nfunction useResizeObserver(element, onResize) {\n    const handleResize = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onResize);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_10__.useLayoutEffect)(()=>{\n        let rAF = 0;\n        if (element) {\n            const resizeObserver = new ResizeObserver(()=>{\n                cancelAnimationFrame(rAF);\n                rAF = window.requestAnimationFrame(handleResize);\n            });\n            resizeObserver.observe(element);\n            return ()=>{\n                window.cancelAnimationFrame(rAF);\n                resizeObserver.unobserve(element);\n            };\n        }\n    }, [\n        element,\n        handleResize\n    ]);\n}\nvar Root = ScrollArea;\nvar Viewport = ScrollAreaViewport;\nvar Scrollbar = ScrollAreaScrollbar;\nvar Thumb = ScrollAreaThumb;\nvar Corner = ScrollAreaCorner;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-scroll-area/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs":
/*!**********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useCallbackRef: () => (/* binding */ useCallbackRef)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-callback-ref/src/useCallbackRef.tsx\n\nfunction useCallbackRef(callback) {\n  const callbackRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(callback);\n  react__WEBPACK_IMPORTED_MODULE_0__.useEffect(() => {\n    callbackRef.current = callback;\n  });\n  return react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => (...args) => callbackRef.current?.(...args), []);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1jYWxsYmFjay1yZWYvZGlzdC9pbmRleC5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUMrQjtBQUMvQjtBQUNBLHNCQUFzQix5Q0FBWTtBQUNsQyxFQUFFLDRDQUFlO0FBQ2pCO0FBQ0EsR0FBRztBQUNILFNBQVMsMENBQWE7QUFDdEI7QUFHRTtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL25vZGVfbW9kdWxlcy9AcmFkaXgtdWkvcmVhY3QtdXNlLWNhbGxiYWNrLXJlZi9kaXN0L2luZGV4Lm1qcz80MWFjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1jYWxsYmFjay1yZWYvc3JjL3VzZUNhbGxiYWNrUmVmLnRzeFxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5mdW5jdGlvbiB1c2VDYWxsYmFja1JlZihjYWxsYmFjaykge1xuICBjb25zdCBjYWxsYmFja1JlZiA9IFJlYWN0LnVzZVJlZihjYWxsYmFjayk7XG4gIFJlYWN0LnVzZUVmZmVjdCgoKSA9PiB7XG4gICAgY2FsbGJhY2tSZWYuY3VycmVudCA9IGNhbGxiYWNrO1xuICB9KTtcbiAgcmV0dXJuIFJlYWN0LnVzZU1lbW8oKCkgPT4gKC4uLmFyZ3MpID0+IGNhbGxiYWNrUmVmLmN1cnJlbnQ/LiguLi5hcmdzKSwgW10pO1xufVxuZXhwb3J0IHtcbiAgdXNlQ2FsbGJhY2tSZWZcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\n");

/***/ }),

/***/ "(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs":
/*!***********************************************************************!*\
  !*** ./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLayoutEffect: () => (/* binding */ useLayoutEffect2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n// packages/react/use-layout-effect/src/useLayoutEffect.tsx\n\nvar useLayoutEffect2 = Boolean(globalThis?.document) ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : () => {\n};\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHJhZGl4LXVpL3JlYWN0LXVzZS1sYXlvdXQtZWZmZWN0L2Rpc3QvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDK0I7QUFDL0IsdURBQXVELGtEQUFxQjtBQUM1RTtBQUdFO0FBQ0YiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vbm9kZV9tb2R1bGVzL0ByYWRpeC11aS9yZWFjdC11c2UtbGF5b3V0LWVmZmVjdC9kaXN0L2luZGV4Lm1qcz9kZGRjIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIHBhY2thZ2VzL3JlYWN0L3VzZS1sYXlvdXQtZWZmZWN0L3NyYy91c2VMYXlvdXRFZmZlY3QudHN4XG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbnZhciB1c2VMYXlvdXRFZmZlY3QyID0gQm9vbGVhbihnbG9iYWxUaGlzPy5kb2N1bWVudCkgPyBSZWFjdC51c2VMYXlvdXRFZmZlY3QgOiAoKSA9PiB7XG59O1xuZXhwb3J0IHtcbiAgdXNlTGF5b3V0RWZmZWN0MiBhcyB1c2VMYXlvdXRFZmZlY3Rcbn07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5tanMubWFwXG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs":
/*!******************************************************************!*\
  !*** ./node_modules/@radix-ui/react-compose-refs/dist/index.mjs ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   composeRefs: () => (/* binding */ composeRefs),\n/* harmony export */   useComposedRefs: () => (/* binding */ useComposedRefs)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n// packages/react/compose-refs/src/composeRefs.tsx\n\nfunction setRef(ref, value) {\n  if (typeof ref === \"function\") {\n    return ref(value);\n  } else if (ref !== null && ref !== void 0) {\n    ref.current = value;\n  }\n}\nfunction composeRefs(...refs) {\n  return (node) => {\n    let hasCleanup = false;\n    const cleanups = refs.map((ref) => {\n      const cleanup = setRef(ref, node);\n      if (!hasCleanup && typeof cleanup == \"function\") {\n        hasCleanup = true;\n      }\n      return cleanup;\n    });\n    if (hasCleanup) {\n      return () => {\n        for (let i = 0; i < cleanups.length; i++) {\n          const cleanup = cleanups[i];\n          if (typeof cleanup == \"function\") {\n            cleanup();\n          } else {\n            setRef(refs[i], null);\n          }\n        }\n      };\n    }\n  };\n}\nfunction useComposedRefs(...refs) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.useCallback(composeRefs(...refs), refs);\n}\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs":
/*!**********************************************************!*\
  !*** ./node_modules/@radix-ui/react-slot/dist/index.mjs ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Slot: () => (/* binding */ Slot),\n/* harmony export */   Slottable: () => (/* binding */ Slottable)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(rsc)/./node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-runtime.js\");\n// packages/react/slot/src/Slot.tsx\n\n\n\nvar Slot = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  const childrenArray = react__WEBPACK_IMPORTED_MODULE_0__.Children.toArray(children);\n  const slottable = childrenArray.find(isSlottable);\n  if (slottable) {\n    const newElement = slottable.props.children;\n    const newChildren = childrenArray.map((child) => {\n      if (child === slottable) {\n        if (react__WEBPACK_IMPORTED_MODULE_0__.Children.count(newElement) > 1) return react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null);\n        return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? newElement.props.children : null;\n      } else {\n        return child;\n      }\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children: react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(newElement) ? react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(newElement, void 0, newChildren) : null });\n  }\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SlotClone, { ...slotProps, ref: forwardedRef, children });\n});\nSlot.displayName = \"Slot\";\nvar SlotClone = react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef) => {\n  const { children, ...slotProps } = props;\n  if (react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(children)) {\n    const childrenRef = getElementRef(children);\n    return react__WEBPACK_IMPORTED_MODULE_0__.cloneElement(children, {\n      ...mergeProps(slotProps, children.props),\n      // @ts-ignore\n      ref: forwardedRef ? (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_2__.composeRefs)(forwardedRef, childrenRef) : childrenRef\n    });\n  }\n  return react__WEBPACK_IMPORTED_MODULE_0__.Children.count(children) > 1 ? react__WEBPACK_IMPORTED_MODULE_0__.Children.only(null) : null;\n});\nSlotClone.displayName = \"SlotClone\";\nvar Slottable = ({ children }) => {\n  return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, { children });\n};\nfunction isSlottable(child) {\n  return react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(child) && child.type === Slottable;\n}\nfunction mergeProps(slotProps, childProps) {\n  const overrideProps = { ...childProps };\n  for (const propName in childProps) {\n    const slotPropValue = slotProps[propName];\n    const childPropValue = childProps[propName];\n    const isHandler = /^on[A-Z]/.test(propName);\n    if (isHandler) {\n      if (slotPropValue && childPropValue) {\n        overrideProps[propName] = (...args) => {\n          childPropValue(...args);\n          slotPropValue(...args);\n        };\n      } else if (slotPropValue) {\n        overrideProps[propName] = slotPropValue;\n      }\n    } else if (propName === \"style\") {\n      overrideProps[propName] = { ...slotPropValue, ...childPropValue };\n    } else if (propName === \"className\") {\n      overrideProps[propName] = [slotPropValue, childPropValue].filter(Boolean).join(\" \");\n    }\n  }\n  return { ...slotProps, ...overrideProps };\n}\nfunction getElementRef(element) {\n  let getter = Object.getOwnPropertyDescriptor(element.props, \"ref\")?.get;\n  let mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.ref;\n  }\n  getter = Object.getOwnPropertyDescriptor(element, \"ref\")?.get;\n  mayWarn = getter && \"isReactWarning\" in getter && getter.isReactWarning;\n  if (mayWarn) {\n    return element.props.ref;\n  }\n  return element.props.ref || element.ref;\n}\nvar Root = Slot;\n\n//# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\n");

/***/ })

};
;