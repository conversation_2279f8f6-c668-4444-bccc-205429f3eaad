"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(ssr)/./node_modules/@vercel/analytics/dist/next/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/next/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Analytics: () => (/* binding */ Analytics2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Analytics auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/analytics\";\nvar version = \"1.5.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.va) return;\n    window.va = function a(...params) {\n        (window.vaq = window.vaq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction setMode(mode = \"auto\") {\n    if (mode === \"auto\") {\n        window.vam = detectEnvironment();\n        return;\n    }\n    window.vam = mode;\n}\nfunction getMode() {\n    const mode = isBrowser() ? window.vam : detectEnvironment();\n    return mode || \"production\";\n}\nfunction isDevelopment() {\n    return getMode() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/script.debug.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/insights/script.js`;\n    }\n    return \"/_vercel/insights/script.js\";\n}\n// src/generic.ts\nfunction inject(props = {\n    debug: true\n}) {\n    var _a;\n    if (!isBrowser()) return;\n    setMode(props.mode);\n    initQueue();\n    if (props.beforeSend) {\n        (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return;\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.disableAutoTrack) {\n        script.dataset.disableAutoTrack = \"1\";\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/insights`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    script.onerror = ()=>{\n        const errorMessage = isDevelopment() ? \"Please check if any ad blockers are enabled and try again.\" : \"Be sure to enable Web Analytics for your project and deploy again. See https://vercel.com/docs/analytics/quickstart for more information.\";\n        console.log(`[Vercel Web Analytics] Failed to load script from ${src}. ${errorMessage}`);\n    };\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    document.head.appendChild(script);\n}\nfunction pageview({ route, path }) {\n    var _a;\n    (_a = window.va) == null ? void 0 : _a.call(window, \"pageview\", {\n        route,\n        path\n    });\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction Analytics(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _a;\n        if (props.beforeSend) {\n            (_a = window.va) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n        }\n    }, [\n        props.beforeSend\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        inject({\n            framework: props.framework || \"react\",\n            basePath: props.basePath ?? getBasePath(),\n            ...props.route !== void 0 && {\n                disableAutoTrack: true\n            },\n            ...props\n        });\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (props.route && props.path) {\n            pageview({\n                route: props.route,\n                path: props.path\n            });\n        }\n    }, [\n        props.route,\n        props.path\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return {\n            route: null,\n            path\n        };\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return {\n        route: computeRoute(path, finalParams),\n        path\n    };\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction AnalyticsComponent(props) {\n    const { route, path } = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Analytics, {\n        path,\n        route,\n        ...props,\n        basePath: getBasePath2(),\n        framework: \"next\"\n    });\n}\nfunction Analytics2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(AnalyticsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/analytics/dist/next/index.mjs\n");

/***/ }),

/***/ "(rsc)/./node_modules/@vercel/analytics/dist/next/index.mjs":
/*!************************************************************!*\
  !*** ./node_modules/@vercel/analytics/dist/next/index.mjs ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Analytics: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/home/<USER>/Arya/Projects/cid/v0-ufdr-analysis-tool/node_modules/@vercel/analytics/dist/next/index.mjs#Analytics`);


/***/ })

};
;