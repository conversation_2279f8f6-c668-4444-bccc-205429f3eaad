{"version": 3, "file": "write-entry.d.ts", "sourceRoot": "", "sources": ["../../src/write-entry.ts"], "names": [], "mappings": ";;;;AAAA,OAAW,EAAE,KAAK,KAAK,EAAE,MAAM,IAAI,CAAA;AACnC,OAAO,EAAE,QAAQ,EAAE,MAAM,UAAU,CAAA;AAEnC,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAA;AAGpC,OAAO,EAGL,UAAU,EACV,qBAAqB,EACtB,MAAM,cAAc,CAAA;AAErB,OAAO,EAAE,SAAS,EAAE,MAAM,iBAAiB,CAAA;AAG3C,OAAO,EAAE,aAAa,EAAE,MAAM,YAAY,CAAA;AAC1C,OAAO,EACL,QAAQ,EACR,MAAM,EACN,SAAS,EAEV,MAAM,kBAAkB,CAAA;AAazB,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,IAAI,eAAiB,CAAA;AAC3B,QAAA,MAAM,SAAS,eAAsB,CAAA;AACrC,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,QAAQ,eAAqB,CAAA;AACnC,QAAA,MAAM,MAAM,eAAmB,CAAA;AAC/B,QAAA,MAAM,IAAI,eAAiB,CAAA;AAC3B,QAAA,MAAM,KAAK,eAAkB,CAAA;AAC7B,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,MAAM,eAAmB,CAAA;AAC/B,QAAA,MAAM,UAAU,eAAuB,CAAA;AACvC,QAAA,MAAM,QAAQ,eAAqB,CAAA;AACnC,QAAA,MAAM,UAAU,eAAuB,CAAA;AACvC,QAAA,MAAM,KAAK,eAAkB,CAAA;AAC7B,QAAA,MAAM,IAAI,eAAiB,CAAA;AAC3B,QAAA,MAAM,UAAU,eAAuB,CAAA;AACvC,QAAA,MAAM,OAAO,eAAoB,CAAA;AACjC,QAAA,MAAM,MAAM,eAAmB,CAAA;AAE/B,qBAAa,UACX,SAAQ,QAAQ,CAAC,MAAM,EAAE,QAAQ,CAAC,cAAc,EAAE,SAAS,CAC3D,YAAW,MAAM;;IAEjB,IAAI,EAAE,MAAM,CAAA;IACZ,QAAQ,EAAE,OAAO,CAAA;IACjB,KAAK,EAAE,MAAM,CAA4C;IAEzD,MAAM,EAAE,MAAM,CAAyB;IACvC,WAAW,EAAE,MAAM,CAAA;IACnB,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAA;IACtD,SAAS,EAAE,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,SAAS,CAAC,CAAA;IACtD,aAAa,EAAE,OAAO,CAAA;IACtB,GAAG,EAAE,MAAM,CAAA;IACX,MAAM,EAAE,OAAO,CAAA;IACf,KAAK,CAAC,EAAE,IAAI,CAAA;IACZ,KAAK,EAAE,OAAO,CAAA;IACd,OAAO,EAAE,OAAO,CAAA;IAChB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,EAAE,CAAC,EAAE,MAAM,CAAA;IAEX,QAAQ,EAAE,MAAM,CAAI;IACpB,WAAW,EAAE,MAAM,CAAI;IACvB,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ,GAAG,EAAE,MAAM,CAAI;IACf,MAAM,EAAE,MAAM,CAAI;IAClB,MAAM,EAAE,MAAM,CAAI;IAClB,MAAM,EAAE,MAAM,CAAI;IAElB,KAAK,EAAE,OAAO,CAAA;IACd,QAAQ,EAAE,MAAM,CAAA;IAEhB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,IAAI,CAAC,EAAE,aAAa,GAAG,aAAa,CAAA;IACpC,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,IAAI,CAAC,EAAE,KAAK,CAAA;IACZ,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,GAAG,CAAA;gBAI7B,CAAC,EAAE,MAAM,EAAE,IAAI,GAAE,qBAA0B;IAmEvD,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,GAAE,QAAa;IAI/D,IAAI,CAAC,EAAE,EAAE,MAAM,SAAS,EAAE,GAAG,IAAI,EAAE,GAAG,EAAE;IAOxC,CAAC,KAAK,CAAC;IASP,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,KAAK;IAWrB,CAAC,OAAO,CAAC;IAcT,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM;IAInB,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM;IAIrB,CAAC,MAAM,CAAC;IAqER,CAAC,SAAS,CAAC;IAcX,CAAC,OAAO,CAAC;IAST,CAAC,UAAU,CAAC,CAAC,QAAQ,EAAE,MAAM;IAM7B,CAAC,QAAQ,CAAC,CAAC,QAAQ,EAAE,MAAM;IAe3B,CAAC,IAAI,CAAC;IAwBN,CAAC,QAAQ,CAAC;IASV,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,MAAM;IAsBvB,CAAC,IAAI,CAAC;IAgBN,CAAC,KAAK,CAAC,CACL,EAAE,GAAE,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC,cAAc,KAAK,GAAc;IAMnE,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,MAAM;IA8D1B,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,MAAM,GAAG;IAI1B,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,OAAO;IACxD,KAAK,CACH,GAAG,EAAE,MAAM,GAAG,MAAM,EACpB,QAAQ,CAAC,EAAE,cAAc,GAAG,IAAI,EAChC,EAAE,CAAC,EAAE,MAAM,IAAI,GACd,OAAO;IAmCV,CAAC,OAAO,CAAC;CA2BV;AAED,qBAAa,cAAe,SAAQ,UAAW,YAAW,MAAM;IAC9D,IAAI,EAAE,IAAI,CAAQ;IAElB,CAAC,KAAK,CAAC;IAIP,CAAC,OAAO,CAAC;IAIT,CAAC,QAAQ,CAAC;IAIV,CAAC,IAAI,CAAC;IAuBN,CAAC,UAAU,CAAC,CAAC,EAAE,EAAE,MAAM,GAAG;IAK1B,CAAC,KAAK,CAAC,CACL,EAAE,GAAE,CAAC,EAAE,CAAC,EAAE,IAAI,GAAG,KAAK,GAAG,MAAM,CAAC,cAAc,KAAK,GAAc;CAMpE;AAED,qBAAa,aACX,SAAQ,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,SAAS,CACnD,YAAW,MAAM;IAEjB,QAAQ,EAAE,MAAM,CAAI;IACpB,WAAW,EAAE,MAAM,CAAI;IACvB,GAAG,EAAE,MAAM,CAAI;IACf,GAAG,EAAE,MAAM,CAAI;IACf,MAAM,EAAE,MAAM,CAAI;IAClB,MAAM,EAAE,MAAM,CAAI;IAClB,aAAa,EAAE,OAAO,CAAA;IACtB,QAAQ,EAAE,OAAO,CAAA;IACjB,MAAM,EAAE,OAAO,CAAA;IACf,KAAK,EAAE,OAAO,CAAA;IACd,OAAO,EAAE,OAAO,CAAA;IAChB,SAAS,EAAE,SAAS,CAAA;IACpB,IAAI,EAAE,aAAa,CAAA;IACnB,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,IAAI,EAAE,MAAM,CAAA;IACZ,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ,GAAG,CAAC,EAAE,MAAM,CAAA;IACZ,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,KAAK,CAAC,EAAE,MAAM,CAAA;IACd,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,KAAK,CAAC,EAAE,IAAI,CAAA;IACZ,KAAK,CAAC,EAAE,IAAI,CAAA;IACZ,KAAK,CAAC,EAAE,IAAI,CAAA;IACZ,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,IAAI,EAAE,MAAM,CAAA;IACZ,YAAY,CAAC,EAAE,CAAC,KAAK,EAAE,UAAU,KAAK,GAAG,CAAA;IAEzC,IAAI,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,GAAE,QAAa;gBAK7D,SAAS,EAAE,SAAS,EACpB,IAAI,GAAE,qBAA0B;IAyHlC,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,MAAM;IAIrB,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM;IAInB,KAAK,CAAC,MAAM,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,OAAO;IACxD,KAAK,CACH,GAAG,EAAE,MAAM,GAAG,MAAM,EACpB,QAAQ,CAAC,EAAE,cAAc,GAAG,IAAI,EAChC,EAAE,CAAC,EAAE,MAAM,IAAI,GACd,OAAO;IA0BV,GAAG,CAAC,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,IAAI;IAC1B,GAAG,CAAC,KAAK,EAAE,MAAM,GAAG,MAAM,EAAE,EAAE,CAAC,EAAE,MAAM,IAAI,GAAG,IAAI;IAClD,GAAG,CACD,KAAK,EAAE,MAAM,GAAG,MAAM,EACtB,QAAQ,CAAC,EAAE,cAAc,EACzB,EAAE,CAAC,EAAE,MAAM,IAAI,GACd,IAAI;CA2BR"}